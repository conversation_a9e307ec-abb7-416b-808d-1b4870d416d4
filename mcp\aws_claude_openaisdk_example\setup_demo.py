#!/usr/bin/env python3
"""
Setup script for Bedrock Claude + OpenAI Agent SDK + MCP Demo
"""
import subprocess
import sys
import os


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        subprocess.run(command, shell=True, check=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        return False


def check_prerequisites():
    """Check if required tools are installed"""
    print("🔍 Checking prerequisites...")

    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    print(f"✅ Python {sys.version.split()[0]} found")

    # Check if npx is available
    try:
        subprocess.run(["npx", "--version"],
                      capture_output=True, check=True)
        print("✅ npx found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ npx not found. Please install Node.js from https://nodejs.org/")
        return False

    # Check if AWS CLI is available
    try:
        subprocess.run(["aws", "--version"],
                      capture_output=True, check=True)
        print("✅ AWS CLI found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("⚠️  AWS CLI not found. Installing it now...")
        if not run_command("pip install awscli", "Installing AWS CLI"):
            return False

    return True


def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")

    commands = [
        ("pip install --upgrade pip", "Upgrading pip"),
        ("pip install -r requirements.txt", "Installing project dependencies"),
    ]

    for command, description in commands:
        if not run_command(command, description):
            return False

    return True


def setup_aws_instructions():
    """Provide AWS setup instructions"""
    print("\n🔐 AWS Configuration Required:")
    print("=" * 40)
    print("1. Configure AWS credentials:")
    print("   aws configure")
    print("   (Enter your Access Key ID, Secret Access Key, and region 'us-west-2')")
    print()
    print("2. Enable Bedrock model access:")
    print("   - Go to AWS Console > Amazon Bedrock > Model Access")
    print("   - Request access to 'Anthropic Claude Sonnet 4'")
    print("   - Wait for approval (usually instant for most accounts)")
    print()
    print("3. Test your setup:")
    print("   python bedrock_claude_demo.py")
    print("=" * 40)


def main():
    """Main setup function"""
    print("🚀 Bedrock Claude + OpenAI Agent SDK Demo Setup")
    print("=" * 50)

    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please resolve the issues above.")
        return 1

    if not install_dependencies():
        print("\n❌ Dependency installation failed.")
        return 1

    setup_aws_instructions()

    print("\n✅ Setup completed successfully!")
    print("🎉 You're ready to run the demo!")
    print("   python bedrock_claude_demo.py")

    return 0


if __name__ == "__main__":
    sys.exit(main())