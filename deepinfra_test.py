from agents import Agent, Runner, OpenAIChatCompletionsModel
from openai import Async<PERSON>penAI

external_client = AsyncOpenAI(
    base_url='https://api.deepinfra.com/v1/openai',
    api_key='9V7nTFzo2f7VSQa61OvPu5eJjqhg9xR2',
)

external_model = OpenAIChatCompletionsModel(
    model="deepseek-ai/DeepSeek-V3-0324",
    openai_client=external_client,
)

# Assign clients to specific agents
outline_generator = Agent(
    name="Story Outline Generator",
    instructions="Generate a detailed outline for a story based on the provided prompt.",
    model=external_model
)

story_writer = Agent(
    name="Story Writer",
    instructions="Write a compelling story based on the provided outline.",
    model=external_model
)

story_editor = Agent(
    name="Story Editor",
    instructions="Edit the story for grammar, style, and coherence.",
    model=external_model
)


def generate_story(prompt):
    # Generate the story outline
    outline_result = Runner.run_sync(
        starting_agent=outline_generator,
        input=prompt
    )
    outline = outline_result.final_output
    print(f"\n\n--- Outline ({outline_generator.name}) ---\n{outline}\n")

    # Write the story based on the outline
    story_result = Runner.run_sync(
        starting_agent=story_writer,
        input=outline
    )
    story = story_result.final_output
    print(f"\n\n--- Story ({story_writer.name}) ---\n{story}\n")

    # Edit the story for final output
    edited_story_result = Runner.run_sync(
        starting_agent=story_editor,
        input=story
    )
    final_story = edited_story_result.final_output
    print(f"\n\n--- Edited Story ({story_editor.name}) ---\n{final_story}\n")

    return final_story


story_prompt = "A story about a young girl who discovers she has the ability to time travel. Write in Chinese."
final_story = generate_story(story_prompt)
print(f"\n\n--- Final Story ---\n{final_story}\n")
