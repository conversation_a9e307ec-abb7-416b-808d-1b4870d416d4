import json
from agents import Agent, <PERSON>, OpenAIChatCompletionsModel
from openai import AsyncOpenAI

# DeepInfra
deepinfra_client = AsyncOpenAI(
    base_url='https://api.deepinfra.com/v1/openai',
    api_key='9V7nTFzo2f7VSQa61OvPu5eJjqhg9xR2',
)

deepinfra_model = OpenAIChatCompletionsModel(
    model="deepseek-ai/DeepSeek-V3-0324",
    openai_client=deepinfra_client,
)

# OpenRouter
openrouter_client = AsyncOpenAI(
    base_url='https://openrouter.ai/api/v1',
    api_key='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',
)

# google/gemini-2.5-pro-preview-03-25 // Paid per 1M Tokens. Input $1.25 and Output $10
# google/gemini-2.5-pro-exp-03-25:free // Free but limited, possible to hit rate limit or quota
openrouter_model = OpenAIChatCompletionsModel(
    model="google/gemini-2.5-pro-preview-03-25",
    openai_client=openrouter_client,
)

google_client = AsyncOpenAI(
    base_url='https://generativelanguage.googleapis.com/v1beta/openai/',
    api_key='AIzaSyBkqLHNb4pCiOeDMWM5YNd77r_8MTL-ys8',
)

google_model = OpenAIChatCompletionsModel(
    model="gemini-2.5-pro-preview-03-25",
    openai_client=google_client,
)

# Assign clients to specific agents
# There are 4 agents in total, 1 outline generator, 1 story writer, 1 story rewriter, 1 story editor
# - outline generator is in charge of creating the story outline
# - story writer is in charge of writing the story based on the outline
# - story rewriter is in charge of rewriting the story based on the editor's comments
# - story editor is in charge of providing feedback on the story

outline_generator = Agent(
    name="Story Outline Generator",
    instructions="Create a detailed story outline based on the request. Output only the outline text in a structured format.",
    model=google_model
)

story_writer_with_outline = Agent(
    name="Story Writer",
    instructions="Write a compelling story using the provided outline and its language. Output only the story text including title and chapter titles if any.",
    model=google_model
)

story_rewriter_with_editor_comments = Agent(
    name="Story Rewriter",
    instructions="Incorporate the editor's comments into the story, keeping the original language. Output just the revised story.",
    model=google_model
)

story_editor = Agent(
    name="Story Editor",
    instructions="""
    Provide constructive feedback on the story, focusing on areas for improvement in plot, character development, pacing, style, dialogue, grammar, and overall impact. Offer specific examples and actionable suggestions.
    Provide above comments (in the story's language) and a score (0-10) formatted within <comments> tags and JSON structure like below:
    <comments>
    {
        "comments": [
            "comment1",
            "comment2",
            "comment3"
        ],
        "score": 8.1
    }
    </comments>
    """,
    model=deepinfra_model
)

# Print the model name used for each agent
print("\n--- Model Names Used for Each Agent ---")
if isinstance(outline_generator.model, OpenAIChatCompletionsModel):
    model_name = outline_generator.model.model
    print(f"Outline Generator Model: {model_name}")
if isinstance(story_writer_with_outline.model, OpenAIChatCompletionsModel):
    model_name = story_writer_with_outline.model.model
    print(f"Story Writer Model: {model_name}")
if isinstance(story_rewriter_with_editor_comments.model, OpenAIChatCompletionsModel):
    model_name = story_rewriter_with_editor_comments.model.model
    print(f"Story Rewriter Model: {model_name}")
if isinstance(story_editor.model, OpenAIChatCompletionsModel):
    model_name = story_editor.model.model
    print(f"Story Editor Model: {model_name}")


def run_agent(agent, input_text):
    response = Runner.run_sync(
        starting_agent=agent,
        input=input_text
    )
    return response.final_output


def extract_json_from_comments(editor_output):
    start_tag = "<comments>"
    end_tag = "</comments>"

    start_index = editor_output.find(start_tag)
    end_index = editor_output.find(end_tag)

    if start_index == -1 or end_index == -1:
        raise ValueError("Comments tags not found in the editor output.")

    # Extract the JSON string between the tags
    json_str = editor_output[start_index + len(start_tag):end_index].strip()

    return json_str


def extract_score_and_comments(editor_output):
    try:
        json_str = extract_json_from_comments(editor_output)
        # print("Extracted JSON String:", json_str)  # Debugging: Print the JSON string

        json_data = json.loads(json_str)
        score = json_data.get("score")
        comments = "\n".join(json_data.get("comments", []))
        return score, comments
    except json.JSONDecodeError as e:
        print("JSONDecodeError:", e)
        return None, []
    except ValueError as e:
        print("ValueError:", e)
        return None, []


def generate_story_with_feedback(prompt, pass_score=8.0, max_versions=5):
    # Generate the story outline
    outline = run_agent(outline_generator, prompt)
    print(f"\n\n--- Outline from outline generator ({outline_generator.name}) ---\n{outline}\n", flush=True)

    version = 1
    comments = None
    score = None
    story = None

    # Write the story based on the outline
    story = run_agent(story_writer_with_outline, outline)
    print(f"\n\n--- Version {version} Story from writer ({story_writer_with_outline.name}) ---\n{story}\n", flush=True)

    while version <= max_versions:

        # Edit the story for final output
        editor_comments = run_agent(story_editor, story)
        print(f"\n\n--- Version {version} Comments from editor ({story_editor.name}) ---\n{editor_comments}\n", flush=True)

        score, comments = extract_score_and_comments(editor_comments)

        if (score is not None and score >= pass_score) or version == max_versions:
            break

        story_with_editor_comments = f"Rewrite the story based on the editor's comments:\n{comments}\n\nStory:\n{story}"

        version += 1

        # Write the story based on the outline
        story = run_agent(story_rewriter_with_editor_comments, story_with_editor_comments)
        print(f"\n\n--- Version {version} Story from rewriter ({story_rewriter_with_editor_comments.name}) ---\n{story}\n", flush=True)

    return story, comments, score, version


story_prompt = "写一个中文小说，篇幅中篇，奇幻题材，主角是一只柯基，名叫宾宾，关于它在新加坡的丛林中冒险的故事。"
final_story, final_comments, final_score, final_run_count = generate_story_with_feedback(story_prompt, pass_score=9.5, max_versions=3)
print(
    f"\n\n--- Final Output ---\n\nInput Prompt:\n{story_prompt}\n\nStory:\n{final_story}\n\nComments:\n{final_comments}\n\nScore: {final_score}\n\nRun Count: {final_run_count}\n", flush=True)
