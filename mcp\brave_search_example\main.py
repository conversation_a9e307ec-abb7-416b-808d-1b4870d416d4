import asyncio
import os
import shutil
import logging
from dotenv import load_dotenv

from agents import Agent, Runner
from agents.mcp import MCPServer, MCPServerStdio

# Load environment variables from .env file
load_dotenv()

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RobustMCPServer:
    """Wrapper for MCPServerStdio with automatic retry and recovery"""

    def __init__(self, name, params, max_retries=3, retry_delay=2.0, enable_monitoring=False, monitor_interval=30.0):
        self.name = name
        self.params = params
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.current_server = None
        self.is_closed = False
        self.enable_monitoring = enable_monitoring
        self.monitor_interval = monitor_interval
        self.monitor_task = None

    async def __aenter__(self):
        """Enter the context manager with retry logic"""
        for attempt in range(self.max_retries + 1):
            try:
                logger.info(f"Attempting to start MCP server (attempt {attempt + 1}/{self.max_retries + 1})")
                self.current_server = MCPServerStdio(
                    name=self.name,
                    params=self.params
                )
                server = await self.current_server.__aenter__()
                logger.info("MCP server started successfully")

                # Start background monitoring if enabled
                if self.enable_monitoring:
                    self.monitor_task = asyncio.create_task(self._background_monitor())

                return self
            except Exception as e:
                logger.error(f"Failed to start MCP server on attempt {attempt + 1}: {e}")
                if self.current_server:
                    try:
                        await self.current_server.__aexit__(None, None, None)
                    except:
                        pass
                    self.current_server = None

                if attempt < self.max_retries:
                    logger.info(f"Retrying in {self.retry_delay} seconds...")
                    await asyncio.sleep(self.retry_delay)
                else:
                    raise Exception(f"Failed to start MCP server after {self.max_retries + 1} attempts")

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit the context manager"""
        self.is_closed = True

        # Cancel background monitoring
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            self.monitor_task = None

        if self.current_server:
            try:
                await self.current_server.__aexit__(exc_type, exc_val, exc_tb)
            except Exception as e:
                logger.error(f"Error closing MCP server: {e}")
            finally:
                self.current_server = None

    async def list_tools(self):
        """List tools with automatic retry on failure"""
        return await self._execute_with_retry(lambda: self.current_server.list_tools())

    async def call_tool(self, name, arguments=None):
        """Call tool with automatic retry on failure"""
        return await self._execute_with_retry(lambda: self.current_server.call_tool(name, arguments))

    async def _execute_with_retry(self, operation):
        """Execute an operation with retry logic if the server fails"""
        for attempt in range(self.max_retries + 1):
            try:
                if not self.current_server:
                    raise Exception("No active MCP server")
                return await operation()
            except Exception as e:
                logger.error(f"MCP operation failed on attempt {attempt + 1}: {e}")

                if attempt < self.max_retries and not self.is_closed:
                    logger.info("Attempting to restart MCP server...")
                    try:
                        # Close the current server
                        if self.current_server:
                            await self.current_server.__aexit__(None, None, None)

                        # Start a new server
                        self.current_server = MCPServerStdio(
                            name=self.name,
                            params=self.params
                        )
                        await self.current_server.__aenter__()
                        logger.info("MCP server restarted successfully")

                        # Retry the operation
                        continue
                    except Exception as restart_error:
                        logger.error(f"Failed to restart MCP server: {restart_error}")
                        self.current_server = None

                        if attempt < self.max_retries:
                            await asyncio.sleep(self.retry_delay)
                else:
                    raise Exception(f"Operation failed after {self.max_retries + 1} attempts: {e}")

    def __getattr__(self, name):
        """Delegate other attributes to the current server"""
        if self.current_server:
            return getattr(self.current_server, name)
        raise AttributeError(f"No active server and no attribute '{name}'")

    async def health_check(self):
        """Perform a health check on the current server"""
        try:
            if not self.current_server:
                return False

            # Try to list tools as a health check
            await self.current_server.list_tools()
            return True
        except Exception as e:
            logger.warning(f"Health check failed: {e}")
            return False

    async def ensure_healthy(self):
        """Ensure the server is healthy, restart if necessary"""
        if not await self.health_check():
            logger.info("Server health check failed, attempting restart...")
            try:
                if self.current_server:
                    await self.current_server.__aexit__(None, None, None)

                self.current_server = MCPServerStdio(
                    name=self.name,
                    params=self.params
                )
                await self.current_server.__aenter__()
                logger.info("Server restarted successfully")
                return True
            except Exception as e:
                logger.error(f"Failed to restart server: {e}")
                self.current_server = None
                return False
        return True

    async def _background_monitor(self):
        """Background task to monitor server health"""
        logger.info(f"Starting background monitoring (interval: {self.monitor_interval}s)")

        while not self.is_closed:
            try:
                await asyncio.sleep(self.monitor_interval)

                if not self.is_closed:
                    logger.debug("Performing background health check...")
                    if not await self.health_check():
                        logger.warning("Background health check failed, attempting restart...")
                        await self.ensure_healthy()
                    else:
                        logger.debug("Background health check passed")

            except asyncio.CancelledError:
                logger.info("Background monitoring cancelled")
                break
            except Exception as e:
                logger.error(f"Error in background monitoring: {e}")
                await asyncio.sleep(self.retry_delay)


def print_agent_info(agent):
    """Print detailed information about the agent"""
    print(f"\n=== AGENT INFORMATION ===")
    print(f"Agent Name: {agent.name}")
    print(f"Instructions: {agent.instructions}")
    print(f"Model: {getattr(agent, 'model', 'Default')}")
    print(f"MCP Servers: {len(agent.mcp_servers) if hasattr(agent, 'mcp_servers') and agent.mcp_servers else 0}")
    if hasattr(agent, 'mcp_servers') and agent.mcp_servers:
        for i, server in enumerate(agent.mcp_servers):
            print(f"  Server {i+1}: {getattr(server, 'name', 'Unknown')}")
    print("=" * 30)


async def print_mcp_tools(mcp_server):
    """Print available MCP tools"""
    try:
        tools = await mcp_server.list_tools()
        print(f"\n=== MCP TOOLS AVAILABLE ===")
        print(f"Server: {getattr(mcp_server, 'name', 'Unknown')}")
        print(f"Number of tools: {len(tools.tools) if hasattr(tools, 'tools') else 0}")
        if hasattr(tools, 'tools'):
            for tool in tools.tools:
                print(f"  - {tool.name}: {tool.description}")
        else:
            print("  No tools information available")
        print("=" * 30)
    except Exception as e:
        print(f"Error listing tools: {e}")


def print_execution_result(result, message):
    """Print detailed execution result information"""
    print(f"\n=== EXECUTION RESULT ===")
    print(f"Query: {message}")
    print(f"Final Output: {result.final_output}")

    # Check if result has additional attributes
    if hasattr(result, 'messages'):
        print(f"Number of messages: {len(result.messages)}")
        for i, msg in enumerate(result.messages):
            print(f"  Message {i+1}: {getattr(msg, 'content', str(msg))[:100]}...")

    if hasattr(result, 'tool_calls'):
        print(f"Tool calls made: {len(result.tool_calls)}")
        for i, call in enumerate(result.tool_calls):
            print(f"  Tool Call {i+1}: {call}")

    # Try to access any additional attributes
    for attr in ['steps', 'iterations', 'agent_steps', 'execution_steps']:
        if hasattr(result, attr):
            value = getattr(result, attr)
            print(f"{attr}: {value}")

    print("=" * 30)


async def run(mcp_server: MCPServer):
    # Get OpenAI API key from environment variables
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("OPENAI_API_KEY not found in environment variables. Please set it in your .env file.")

    # Get agent model from environment variables
    agent_model = os.getenv("AGENT_MODEL")
    if not agent_model:
        raise ValueError("AGENT_MODEL not found in environment variables. Please set it in your .env file.")

    agent = Agent(
        name="Assistant",
        instructions="You can use the brave search service to get online information if needed. When using search tool, try to get at least 10 results or more in one search call.",
        model=agent_model,
        mcp_servers=[mcp_server],
        # If the agents library supports setting OpenAI API key, you can add it here
        # openai_api_key=openai_api_key,  # Uncomment if supported
    )

    # Print agent information
    print_agent_info(agent)

    # Print available MCP tools
    await print_mcp_tools(mcp_server)

    # message = "What tools are you linked to through MCP?"
    message = """
Date and time now is: 2025-05-28 15:58
In our Carebuddy Forum, which is a community for caregivers of dementia patients in Singapore.
There is a post in the forum with below content as quoted by <post> tags:
<post>
Tell me if next week have any dementia community event?
</post>

You as a helpful assistant, will try to post a reply to this user post.
Before write the reply post content, tell me how do you plan to write your reply. Consider following points:
- Is the user asking for any specific information?
- Do you have enought data to provide such information?
- Is there any tool available to you to aquire such information?
- When search, construct a good search query, you may add more keywords to the query to get more relevant results, such as time, region, etc. But do not use search operators in the query.
- Does information required should be limited by time and location and you should filter the data based on these requirement?

Based on your plan, do all the data gathering required. Provide all the data you aquired, filter them when necessary before crafting your reply.
Tell me if these data is sufficient to address the user's request.

Then write a reply forum post. Your name is Sparky, you are a helpful assistant to help caregivers whose loved ones has dementia. Write a warm, empathetic, and conversational reply. The reply should make the original poster feel seen and appreciated for sharing, gently validate their experience or concern, and ask one or two thoughtful questions to encourage further discussion from both the poster and other community members. Keep the tone supportive, approachable and concise. This is forum post, not email, so don't use any signatures.
Output your reply content quoted with <output></output> tags.
    """
    print(f"\n=== RUNNING QUERY ===")
    print(f"Query: {message}")
    print("=" * 30)

    result = await Runner.run(starting_agent=agent, input=message)

    # Print detailed execution results
    print_execution_result(result, message)

    print(f"\n=== FINAL OUTPUT ===")
    print(result.final_output)
    print("=" * 30)


async def main():
    # Get Brave API key from environment variables
    brave_api_key = os.getenv("BRAVE_API_KEY")
    if not brave_api_key:
        raise ValueError("BRAVE_API_KEY not found in environment variables. Please set it in your .env file.")

    try:
        async with RobustMCPServer(
            name="Brave Search Service, via npx",
            params={
                "command": "npx",
                "args": [
                    "-y",
                    "@modelcontextprotocol/server-brave-search"
                ],
                "env": {
                    "BRAVE_API_KEY": brave_api_key
                }
            },
            max_retries=3,  # Number of retry attempts
            retry_delay=2.0,  # Delay between retries in seconds
            enable_monitoring=True,  # Enable background monitoring
            monitor_interval=30.0  # Monitor interval in seconds
        ) as server:
            await run(server)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        raise
    finally:
        # Ensure all resources are closed
        logger.info("Cleaning up...")
        await asyncio.sleep(1)  # Yield control to allow cleanup


if __name__ == "__main__":
    # Let's make sure the user has npx installed
    if not shutil.which("npx"):
        raise RuntimeError("npx is not installed. Please install it with `npm install -g npx`.")

    asyncio.run(main())
