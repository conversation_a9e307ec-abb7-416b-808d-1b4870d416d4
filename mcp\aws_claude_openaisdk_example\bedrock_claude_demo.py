import asyncio
import os
import shutil
from typing import Dict, Any

# OpenAI Agent SDK imports
from agents import Agent, Runner
from agents.mcp import MCPServer, MCPServerStdio

# Note: LiteLLM automatically handles tool schema conversion between OpenAI and Bedrock
# No manual conversion function needed!

async def run(mcp_server: MCPServer):
    """
    Demo showing Claude Sonnet 4 from AWS Bedrock with OpenAI Agent SDK and MCP.
    LiteLLM automatically handles the tool schema conversion.
    """
    agent = Agent(
        name="Assistant",
        instructions=(
            "Use the tools to read the filesystem and answer questions based on those files. "
            "You are helpful and provide detailed responses based on the file contents you can access."
        ),
        model="litellm/bedrock/apac.anthropic.claude-sonnet-4-20250514-v1:0",  # Claude Sonnet 4 via LiteLLM
        mcp_servers=[mcp_server],
    )

    # List the files it can read
    message = "Read the files and list them."
    print(f"🔍 Running: {message}")
    result = await Runner.run(starting_agent=agent, input=message)
    print("📋 Response:", result.final_output)

    # Ask about books
    message = "What is my #1 favorite book? Find the right file and get it from the file content."
    print(f"\n📚 Running: {message}")
    result = await Runner.run(starting_agent=agent, input=message)
    print("📖 Response:", result.final_output)

    # Ask a question that reads then reasons
    message = "Look at my favorite songs. Suggest one new song that I might like based on my preferences."
    print(f"\n🎵 Running: {message}")
    result = await Runner.run(starting_agent=agent, input=message)
    print("🎼 Response:", result.final_output)


async def main():
    """
    Main function to set up MCP filesystem server and run the demo.
    """
    current_dir = os.path.dirname(os.path.abspath(__file__))
    samples_dir = os.path.join(current_dir, "sample_files")

    # Create sample files if they don't exist
    os.makedirs(samples_dir, exist_ok=True)

    # Create sample files for demo
    sample_files = {
        "favorite_books.txt": "My #1 favorite book is 'The Hitchhiker's Guide to the Galaxy' by Douglas Adams.\nMy #2 favorite is 'Dune' by Frank Herbert.\nMy #3 favorite is '1984' by George Orwell.",
        "favorite_songs.txt": "My favorite songs:\n1. Bohemian Rhapsody - Queen\n2. Stairway to Heaven - Led Zeppelin\n3. Hotel California - Eagles\n4. Imagine - John Lennon\n5. Purple Rain - Prince",
        "readme.txt": "This is a demo directory for testing the Claude Sonnet 4 + OpenAI Agent SDK + MCP integration."
    }

    for filename, content in sample_files.items():
        filepath = os.path.join(samples_dir, filename)
        if not os.path.exists(filepath):
            with open(filepath, 'w') as f:
                f.write(content)

    print(f"📁 Using samples directory: {samples_dir}")
    print("🚀 Starting Claude Sonnet 4 + OpenAI Agent SDK + MCP Demo")
    print("=" * 60)

    try:
        # Note: Using standard MCP filesystem server - no tool conversion needed!
        # LiteLLM automatically handles OpenAI <-> Bedrock tool format conversion
        async with MCPServerStdio(
            name="Filesystem Server, via npx",
            params={
                "command": "npx",
                "args": ["-y", "@modelcontextprotocol/server-filesystem", samples_dir]
            },
        ) as server:
            await run(server)
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n💡 Make sure you have:")
        print("1. AWS credentials configured (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY, AWS_REGION_NAME)")
        print("2. npx installed (npm install -g npx)")
        print("3. Required Python packages installed (see requirements.txt)")
    finally:
        # Ensure all resources are closed
        await asyncio.sleep(1)  # Yield control to allow cleanup


if __name__ == "__main__":
    # Check prerequisites
    if not shutil.which("npx"):
        print("❌ npx is not installed. Please install it with `npm install -g npx`.")
        exit(1)

    # Check AWS credentials
    required_env_vars = ["AKIAXOHF6BYRMD5G5RY", "aY0NfapI7QfCY9cLXMhkRTB539ckqac2RH9EETu", "ap-southeast-1"]
    missing_vars = [var for var in required_env_vars if not os.environ.get(var)]

    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("\n💡 Please set your AWS credentials:")
        for var in required_env_vars:
            print(f"   export {var}=your_value_here")
        exit(1)

    print("✅ All prerequisites met!")
    asyncio.run(main())