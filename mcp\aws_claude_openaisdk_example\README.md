# AWS Bedrock Claude Sonnet 4 + OpenAI Agent SDK + MCP Demo

This demo shows how to use **Claude Sonnet 4** from **AWS Bedrock** with the **OpenAI Agent SDK** and **Model Context Protocol (MCP)** filesystem tools.

## ✅ Is This Combination Possible?

**YES!** This combination is fully supported and works seamlessly:

- **OpenAI Agent SDK** (v0.0.12+) supports LiteLLM as a model provider
- **LiteLLM** provides unified access to 100+ LLMs including AWS Bedrock models
- **AWS Bedrock** supports Claude Sonnet 4 via API: `anthropic.claude-sonnet-4-20250514-v1:0`
- **MCP filesystem server** works perfectly with `npx @modelcontextprotocol/server-filesystem`
- **🎯 No tool conversion needed!** LiteLLM automatically handles OpenAI ↔ Bedrock tool schema conversion

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────────┐
│   OpenAI        │    │     LiteLLM      │    │   AWS Bedrock       │
│   Agent SDK     │───▶│   (Auto Tool     │───▶│   Claude Sonnet 4   │
│                 │    │   Conversion)    │    │                     │
└─────────────────┘    └──────────────────┘    └─────────────────────┘
         │                        │
         ▼                        ▼
┌─────────────────┐    ┌──────────────────┐
│      MCP        │    │   Standard       │
│  Filesystem     │    │   OpenAI Tool    │
│   Server        │    │    Format        │
└─────────────────┘    └──────────────────┘
```

## 🚀 Key Benefits

- **No Manual Tool Conversion**: LiteLLM automatically translates between OpenAI and Bedrock tool schemas
- **Unified Interface**: Use OpenAI Agent SDK syntax with any LiteLLM-supported model
- **Full MCP Support**: Access filesystem tools seamlessly through MCP protocol
- **Production Ready**: Built on enterprise-grade AWS Bedrock infrastructure

## 📋 Prerequisites

### 1. **System Requirements**
- Python 3.8 or higher
- Node.js with npm (for npx)

### 2. **AWS Credentials**
Set your AWS credentials as environment variables:
```bash
export AWS_ACCESS_KEY_ID=your_access_key_here
export AWS_SECRET_ACCESS_KEY=your_secret_key_here
export AWS_REGION_NAME=us-east-1  # or your preferred region
```

### 3. **Install Dependencies**
```bash
# Install Python packages
pip install -r requirements.txt

# Install npx (if not already installed)
npm install -g npx
```

## 🎯 Quick Start

### 1. **Run the Demo**
```bash
python bedrock_claude_demo.py
```

### 2. **What It Does**
The demo will:
- Set up an MCP filesystem server
- Create sample files (books, songs, readme)
- Run Claude Sonnet 4 via OpenAI Agent SDK + LiteLLM
- Demonstrate file reading and reasoning capabilities

### 3. **Expected Output**
```
📁 Using samples directory: ./sample_files
🚀 Starting Claude Sonnet 4 + OpenAI Agent SDK + MCP Demo
============================================================

🔍 Running: Read the files and list them.
📋 Response: I can see three files in your directory:
1. favorite_books.txt
2. favorite_songs.txt  
3. readme.txt

📚 Running: What is my #1 favorite book?
📖 Response: Your #1 favorite book is "The Hitchhiker's Guide to the Galaxy" by Douglas Adams.

🎵 Running: Look at my favorite songs. Suggest one new song...
🎼 Response: Based on your classic rock preferences, I recommend "Comfortably Numb" by Pink Floyd.
```

## 🔧 How It Works

### **The Magic: No Tool Conversion Required!**

Previously, developers thought they needed manual tool conversion like:
```python
# ❌ UNNECESSARY - Don't do this!
def convert_openai_tool_to_bedrock_tool(tool):
    # This is redundant - LiteLLM handles this automatically
```

**✅ The Correct Approach:**
```python
# Just use OpenAI Agent SDK normally!
agent = Agent(
    name="Assistant",
    model="litellm/bedrock/anthropic.claude-sonnet-4-20250514-v1:0",
    mcp_servers=[mcp_server],  # MCP tools work directly
)
```

### **LiteLLM Handles Everything**
- **Automatic Schema Translation**: Converts OpenAI tool format to Bedrock format
- **Unified API**: Same code works with 100+ different LLM providers
- **No Code Changes**: Switch between models by just changing the model string

## 📁 Project Structure

```
bedrock_claude_demo.py      # Main demo script
requirements.txt            # Python dependencies  
README.md                  # This file
sample_files/              # Auto-generated sample files
├── favorite_books.txt
├── favorite_songs.txt
└── readme.txt
```

## 🔍 Understanding the Integration

### **OpenAI Agent SDK**
- Provides high-level agent abstractions
- Handles conversation flow and tool calling
- Supports MCP servers out of the box

### **LiteLLM**  
- **Key Feature**: Automatic tool schema conversion
- Translates between OpenAI and provider-specific formats
- Provides unified interface to 100+ LLMs
- Handles authentication and API differences

### **AWS Bedrock**
- Enterprise-grade managed AI service
- Hosts Claude Sonnet 4 and other foundation models
- Provides secure, scalable inference

### **MCP (Model Context Protocol)**
- Standard protocol for connecting tools to LLMs
- Filesystem server provides file reading capabilities
- Works seamlessly with OpenAI Agent SDK

## 🛠️ Troubleshooting

### **Common Issues**

1. **Missing AWS Credentials**
   ```bash
   ❌ Missing required environment variables: AWS_ACCESS_KEY_ID
   ```
   **Solution**: Set your AWS credentials as environment variables

2. **npx Not Found**
   ```bash
   ❌ npx is not installed
   ```
   **Solution**: `npm install -g npx`

3. **Import Errors**
   ```bash
   ModuleNotFoundError: No module named 'agents'
   ```
   **Solution**: `pip install -r requirements.txt`

## 🎯 Next Steps

- **Try Different Models**: Change the model string to use other Bedrock models
- **Add Custom Tools**: Extend with your own MCP servers or function tools
- **Production Deployment**: Use AWS IAM roles and proper credential management
- **Scale Up**: Deploy with LiteLLM proxy for production workloads

## 📚 Further Reading

- [OpenAI Agent SDK Documentation](https://openai.github.io/openai-agents-python/)
- [LiteLLM Bedrock Documentation](https://docs.litellm.ai/docs/providers/bedrock)
- [AWS Bedrock User Guide](https://docs.aws.amazon.com/bedrock/)
- [Model Context Protocol Specification](https://modelcontextprotocol.io/introduction) 