# Brave Search MCP Example

This example demonstrates how to use the Model Context Protocol (MCP) with the official Brave Search server to perform web searches using AI agents.

## Why Brave Search Instead of Google Search?

This example uses the **official Brave Search MCP server** (`@modelcontextprotocol/server-brave-search`) instead of third-party Google Search packages because:

- ✅ **No Timeout Issues**: The official server is more reliable and doesn't suffer from the 60-second timeout problems
- ✅ **Official Support**: Maintained by the Model Context Protocol team
- ✅ **Better Stability**: More robust connection handling and error management
- ✅ **Free Tier**: 2,000 queries per month at no cost
- ✅ **Rich Features**: Supports both web search and local business search

## Quick Start

1. Get a free Brave Search API key from [api.search.brave.com](https://api.search.brave.com/)
2. Create a `.env` file with your API keys (see setup instructions below)
3. Run `python test_connection.py` to verify your setup
4. Run `python main.py` to see the example in action

## Prerequisites

1. **Node.js and npm/npx**: Make sure you have Node.js installed to use npx
2. **Python environment**: With the required dependencies installed
3. **Brave Search API Key**: Get a free API key from [Brave Search API](https://api.search.brave.com/)

## Setup Instructions

### 1. Get a Brave Search API Key

1. Visit [Brave Search API](https://api.search.brave.com/)
2. Sign up for a free account
3. Choose the free tier (2,000 queries per month)
4. Generate your API key from the developer dashboard
5. Copy your API key

### 2. Install Dependencies

Make sure you have the required Python packages installed:

```bash
pip install python-dotenv agents
```

### 3. Configure Environment Variables

Create a `.env` file in this directory with the following content:

```env
# OpenAI API key for the agent model
OPENAI_API_KEY=your_openai_api_key_here

# Agent model to use (e.g., gpt-4, gpt-3.5-turbo)
AGENT_MODEL=gpt-4

# Brave Search API key
BRAVE_API_KEY=your_brave_api_key_here
```

### 4. Update the Code with Your API Key

Open `main.py` and replace `"your_brave_api_key_here"` with your actual Brave Search API key in the `BRAVE_API_KEY` environment variable.

## Running the Example

1. Make sure you have npx installed:
```bash
npm install -g npx
```

2. Test your configuration (recommended):
```bash
python test_connection.py
```

3. If all tests pass, run the main example:
```bash
python main.py
```

## Testing Your Setup

Before running the main example, it's recommended to run the test script to verify everything is configured correctly:

```bash
python test_connection.py
```

This script will check:
- ✅ `.env` file exists and is readable
- ✅ Required Python packages are installed
- ✅ `npx` is available in your system PATH
- ✅ All required API keys are properly set

If any checks fail, the script will provide specific instructions on how to fix the issues.

## What This Example Does

1. **Initializes an MCP Server**: Uses the official `@modelcontextprotocol/server-brave-search` package
2. **Creates an AI Agent**: Sets up an agent with access to the Brave Search tools
3. **Performs a Search Query**: Searches for "contact number of Anda.sg" online
4. **Displays Results**: Shows the search results and agent's response

## Troubleshooting

### Timeout Issues

If you encounter timeout errors, this is a known issue with some MCP servers. The Brave Search server is more reliable than some alternatives like the Google Search servers that have reported timeout issues.

### NPX Not Found

Make sure Node.js and npm are properly installed:
```bash
node --version
npm --version
```

### API Key Issues

- Verify your Brave Search API key is correct
- Check that you haven't exceeded your API quota
- Ensure the API key is properly set in the environment variables

### Connection Issues

- Check your internet connection
- Verify that the Brave Search API is accessible from your network
- Try running with verbose logging to see more details

## Available Tools

The Brave Search MCP server provides these tools:

1. **brave_web_search**: Performs general web searches
   - Parameters: query, count (optional), offset (optional)
   
2. **brave_local_search**: Searches for local businesses and services
   - Parameters: query, count (optional)
   - Automatically falls back to web search if no local results found

## Customizing the Example

You can modify the search query by changing the `message` variable in `main.py`:

```python
message = "Your custom search query here"
```

You can also modify the agent instructions to change how it behaves:

```python
agent = Agent(
    name="Assistant",
    instructions="Your custom instructions here",
    model=agent_model,
    mcp_servers=[mcp_server],
)
```

## Advantages of Brave Search MCP Server

- **Official Support**: Maintained by the Model Context Protocol team
- **Reliability**: More stable than third-party alternatives
- **Features**: Supports both web and local search
- **Free Tier**: 2,000 queries per month at no cost
- **No Timeout Issues**: More reliable connection and response handling

## Further Reading

- [Model Context Protocol Documentation](https://modelcontextprotocol.io/)
- [Brave Search API Documentation](https://api.search.brave.com/docs/)
- [OpenAI Agents Library](https://github.com/openai/agents) 