<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OpenAI Agents Test Project Documentation</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5rem;
        }
        .header p {
            margin: 0.5rem 0 0 0;
            font-size: 1.2rem;
            opacity: 0.9;
        }
        .section {
            background: white;
            margin: 2rem 0;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section h2 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
            margin-top: 0;
        }
        .section h3 {
            color: #764ba2;
            margin-top: 1.5rem;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        .file-tree {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 1rem;
            margin: 1rem 0;
            font-family: 'Courier New', monospace;
        }
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .tech-card {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #667eea;
        }
        .tech-card h4 {
            margin: 0 0 0.5rem 0;
            color: #667eea;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .warning:before {
            content: "⚠️ ";
            font-weight: bold;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 5px;
            padding: 1rem;
            margin: 1rem 0;
        }
        .info:before {
            content: "ℹ️ ";
            font-weight: bold;
        }
        .toc {
            background: #e9ecef;
            padding: 1.5rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
        .toc h3 {
            margin-top: 0;
            color: #495057;
        }
        .toc ul {
            list-style: none;
            padding-left: 0;
        }
        .toc li {
            padding: 0.25rem 0;
        }
        .toc a {
            color: #667eea;
            text-decoration: none;
        }
        .toc a:hover {
            text-decoration: underline;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 1rem 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 0.75rem;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #495057;
        }
        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
            font-weight: bold;
            border-radius: 3px;
            margin: 0.25rem;
        }
        .badge-primary { background: #667eea; color: white; }
        .badge-success { background: #28a745; color: white; }
        .badge-warning { background: #ffc107; color: black; }
        .badge-info { background: #17a2b8; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>OpenAI Agents Test Project</h1>
        <p>Multi-Agent AI Story Generation System with MCP Integrations</p>
    </div>

    <div class="toc">
        <h3>Table of Contents</h3>
        <ul>
            <li><a href="#overview">1. Project Overview</a></li>
            <li><a href="#structure">2. Project Structure</a></li>
            <li><a href="#technologies">3. Technologies Used</a></li>
            <li><a href="#components">4. Key Components</a></li>
            <li><a href="#configuration">5. Configuration Files</a></li>
            <li><a href="#setup">6. Setup Instructions</a></li>
            <li><a href="#api">7. API Documentation</a></li>
            <li><a href="#examples">8. Code Examples</a></li>
            <li><a href="#mcp">9. MCP Integrations</a></li>
            <li><a href="#outputs">10. Test Outputs</a></li>
        </ul>
    </div>

    <div class="section" id="overview">
        <h2>1. Project Overview</h2>
        <p>This project is a comprehensive testing and demonstration environment for the <strong>OpenAI Agents SDK</strong>, showcasing multi-agent AI systems for story generation with various AI model integrations and Model Context Protocol (MCP) server implementations.</p>

        <h3>Main Purpose</h3>
        <ul class="feature-list">
            <li>Demonstrate multi-agent collaborative story generation workflows</li>
            <li>Test integration with multiple AI model providers (OpenAI, DeepInfra, Google Gemini)</li>
            <li>Showcase Model Context Protocol (MCP) server integrations</li>
            <li>Provide examples of agent orchestration and feedback loops</li>
            <li>Test external API integrations (AWS Bedrock, Brave Search)</li>
        </ul>

        <h3>Key Functionality</h3>
        <div class="tech-grid">
            <div class="tech-card">
                <h4>Story Generation Pipeline</h4>
                <p>Multi-stage story creation with outline generation, writing, and editing phases using specialized agents.</p>
            </div>
            <div class="tech-card">
                <h4>Model Provider Testing</h4>
                <p>Comparative testing across different AI models and providers to evaluate performance and capabilities.</p>
            </div>
            <div class="tech-card">
                <h4>MCP Server Integration</h4>
                <p>Integration with filesystem, search, and AWS Bedrock services through standardized MCP protocol.</p>
            </div>
            <div class="tech-card">
                <h4>Feedback Loop System</h4>
                <p>Iterative improvement system with editor feedback and story rewriting capabilities.</p>
            </div>
        </div>
    </div>

    <div class="section" id="structure">
        <h2>2. Project Structure</h2>
        <div class="file-tree">
openai_agents_test/
├── openai_test.py                    # Basic OpenAI GPT-4 story generation
├── deepinfra_test.py                 # DeepInfra DeepSeek model testing
├── deepinfra_test1.py                # Advanced multi-model testing with feedback
├── mcp/                              # Model Context Protocol examples
│   ├── aws_claude_openaisdk_example/ # AWS Bedrock Claude integration
│   │   ├── README.md
│   │   ├── bedrock_claude_demo.py
│   │   ├── requirements.txt
│   │   └── setup_demo.py
│   ├── brave_search_example/         # Brave Search API integration
│   │   ├── README.md
│   │   ├── main.py
│   │   └── test_connection.py
│   └── filesystem_example/           # Filesystem MCP server example
│       ├── README.md
│       ├── main.py
│       └── sample_files/
├── TestOutput/                       # Generated story outputs
│   ├── DeepInfra/                   # DeepInfra model outputs
│   └── OpenAI/                      # OpenAI model outputs
└── Other/
    └── NOTE.md                      # Setup instructions and documentation
        </div>

        <h3>Directory Descriptions</h3>
        <table>
            <tr>
                <th>Directory/File</th>
                <th>Purpose</th>
                <th>Key Features</th>
            </tr>
            <tr>
                <td><code>openai_test.py</code></td>
                <td>Basic story generation using OpenAI GPT-4</td>
                <td>Three-agent pipeline: outline → write → edit</td>
            </tr>
            <tr>
                <td><code>deepinfra_test.py</code></td>
                <td>DeepSeek model testing via DeepInfra API</td>
                <td>External model integration, same pipeline</td>
            </tr>
            <tr>
                <td><code>deepinfra_test1.py</code></td>
                <td>Advanced multi-model testing with feedback loops</td>
                <td>4 agents, iterative improvement, scoring system</td>
            </tr>
            <tr>
                <td><code>mcp/</code></td>
                <td>Model Context Protocol integrations</td>
                <td>AWS Bedrock, Brave Search, Filesystem servers</td>
            </tr>
            <tr>
                <td><code>TestOutput/</code></td>
                <td>Generated story outputs and test results</td>
                <td>Organized by model provider and test type</td>
            </tr>
        </table>
    </div>

    <div class="section" id="technologies">
        <h2>3. Technologies Used</h2>

        <h3>Core Framework</h3>
        <div class="tech-grid">
            <div class="tech-card">
                <h4>OpenAI Agents SDK <span class="badge badge-primary">v0.0.12+</span></h4>
                <p>Primary framework for agent creation, orchestration, and execution. Provides high-level abstractions for multi-agent workflows.</p>
            </div>
            <div class="tech-card">
                <h4>Model Context Protocol (MCP) <span class="badge badge-info">Latest</span></h4>
                <p>Standardized protocol for connecting AI agents to external tools and services like filesystems and APIs.</p>
            </div>
        </div>

        <h3>AI Model Providers</h3>
        <div class="tech-grid">
            <div class="tech-card">
                <h4>OpenAI <span class="badge badge-success">GPT-4o-mini</span></h4>
                <p>Primary model for story generation tasks. Used in basic testing scenarios.</p>
            </div>
            <div class="tech-card">
                <h4>DeepInfra <span class="badge badge-warning">DeepSeek-V3</span></h4>
                <p>Alternative model provider for comparative testing. Uses DeepSeek-ai/DeepSeek-V3-0324 model.</p>
            </div>
            <div class="tech-card">
                <h4>Google Gemini <span class="badge badge-info">2.5-Pro</span></h4>
                <p>Google's latest model via OpenRouter and direct API. Used for advanced testing scenarios.</p>
            </div>
            <div class="tech-card">
                <h4>AWS Bedrock <span class="badge badge-primary">Claude Sonnet 4</span></h4>
                <p>Enterprise-grade Claude model via AWS Bedrock with LiteLLM integration.</p>
            </div>
        </div>

        <h3>Supporting Libraries</h3>
        <table>
            <tr>
                <th>Library</th>
                <th>Version</th>
                <th>Purpose</th>
            </tr>
            <tr>
                <td>openai</td>
                <td>Latest</td>
                <td>OpenAI API client for model interactions</td>
            </tr>
            <tr>
                <td>litellm</td>
                <td>≥1.53.5</td>
                <td>Unified interface for multiple LLM providers</td>
            </tr>
            <tr>
                <td>boto3</td>
                <td>≥1.34.0</td>
                <td>AWS SDK for Bedrock integration</td>
            </tr>
            <tr>
                <td>python-dotenv</td>
                <td>Latest</td>
                <td>Environment variable management</td>
            </tr>
            <tr>
                <td>asyncio</td>
                <td>Built-in</td>
                <td>Asynchronous programming support</td>
            </tr>
        </table>
    </div>

    <div class="section" id="components">
        <h2>4. Key Components</h2>

        <h3>Story Generation Agents</h3>
        <div class="tech-grid">
            <div class="tech-card">
                <h4>Story Outline Generator</h4>
                <p><strong>Purpose:</strong> Creates detailed story outlines based on user prompts</p>
                <p><strong>Instructions:</strong> "Generate a detailed outline for a story based on the provided prompt."</p>
                <p><strong>Models Used:</strong> GPT-4o-mini, DeepSeek-V3, Gemini 2.5-Pro</p>
            </div>
            <div class="tech-card">
                <h4>Story Writer</h4>
                <p><strong>Purpose:</strong> Writes compelling stories based on provided outlines</p>
                <p><strong>Instructions:</strong> "Write a compelling story based on the provided outline."</p>
                <p><strong>Models Used:</strong> GPT-4o-mini, DeepSeek-V3, Gemini 2.5-Pro</p>
            </div>
            <div class="tech-card">
                <h4>Story Editor</h4>
                <p><strong>Purpose:</strong> Provides feedback and edits stories for improvement</p>
                <p><strong>Instructions:</strong> "Edit the story for grammar, style, and coherence, ensuring it is polished and ready for publication."</p>
                <p><strong>Models Used:</strong> GPT-4o-mini, DeepSeek-V3</p>
            </div>
            <div class="tech-card">
                <h4>Story Rewriter</h4>
                <p><strong>Purpose:</strong> Incorporates editor feedback to improve stories</p>
                <p><strong>Instructions:</strong> "Incorporate the editor's comments into the story, keeping the original language."</p>
                <p><strong>Models Used:</strong> Gemini 2.5-Pro</p>
            </div>
        </div>

        <h3>Agent Workflow Patterns</h3>
        <table>
            <tr>
                <th>Pattern</th>
                <th>Implementation</th>
                <th>Use Case</th>
            </tr>
            <tr>
                <td><strong>Sequential Pipeline</strong></td>
                <td>Outline → Write → Edit</td>
                <td>Basic story generation (openai_test.py, deepinfra_test.py)</td>
            </tr>
            <tr>
                <td><strong>Feedback Loop</strong></td>
                <td>Write → Edit → Rewrite (iterative)</td>
                <td>Quality improvement with scoring (deepinfra_test1.py)</td>
            </tr>
            <tr>
                <td><strong>MCP Integration</strong></td>
                <td>Agent + External Tools</td>
                <td>File operations, web search, cloud services</td>
            </tr>
        </table>

        <h3>Core Functions</h3>
        <div class="code-block">
def generate_story(prompt):
    # Generate the story outline
    outline_result = Runner.run_sync(
        starting_agent=outline_generator,
        input=prompt
    )
    outline = outline_result.final_output

    # Write the story based on the outline
    story_result = Runner.run_sync(
        starting_agent=story_writer,
        input=outline
    )
    story = story_result.final_output

    # Edit the story for final output
    edited_story_result = Runner.run_sync(
        starting_agent=story_editor,
        input=story
    )
    final_story = edited_story_result.final_output

    return final_story
        </div>
    </div>

    <div class="section" id="configuration">
        <h2>5. Configuration Files</h2>

        <h3>Requirements Files</h3>
        <div class="tech-grid">
            <div class="tech-card">
                <h4>AWS Bedrock Example</h4>
                <div class="code-block">
# mcp/aws_claude_openaisdk_example/requirements.txt
openai-agents>=0.0.12
litellm>=1.53.5
boto3>=1.34.0
                </div>
            </div>
            <div class="tech-card">
                <h4>Brave Search Example</h4>
                <div class="code-block">
# Brave Search MCP dependencies
python-dotenv
agents
                </div>
            </div>
        </div>

        <h3>Environment Configuration</h3>
        <div class="warning">
            <strong>Security Note:</strong> Never commit API keys to version control. Use environment variables or .env files that are excluded from git.
        </div>

        <h4>Required Environment Variables</h4>
        <table>
            <tr>
                <th>Variable</th>
                <th>Purpose</th>
                <th>Example</th>
            </tr>
            <tr>
                <td><code>OPENAI_API_KEY</code></td>
                <td>OpenAI API authentication</td>
                <td>sk-uw4fqsQflceqCkMKaeDUT3BlbkFJ...</td>
            </tr>
            <tr>
                <td><code>AWS_ACCESS_KEY_ID</code></td>
                <td>AWS Bedrock access</td>
                <td>AKIAXOHF6BYRMD5G5RY</td>
            </tr>
            <tr>
                <td><code>AWS_SECRET_ACCESS_KEY</code></td>
                <td>AWS Bedrock secret</td>
                <td>aY0NfapI7QfCY9cLXMhkRTB539ck...</td>
            </tr>
            <tr>
                <td><code>AWS_REGION_NAME</code></td>
                <td>AWS region</td>
                <td>ap-southeast-1</td>
            </tr>
            <tr>
                <td><code>BRAVE_API_KEY</code></td>
                <td>Brave Search API</td>
                <td>BSA-xxx-xxx-xxx</td>
            </tr>
        </table>

        <h4>Sample .env File</h4>
        <div class="code-block">
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
AGENT_MODEL=gpt-4

# AWS Bedrock Configuration
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION_NAME=us-east-1

# Brave Search Configuration
BRAVE_API_KEY=your_brave_api_key_here
        </div>
    </div>

    <div class="section" id="setup">
        <h2>6. Setup Instructions</h2>

        <h3>Prerequisites</h3>
        <ul class="feature-list">
            <li>Python 3.8 or higher</li>
            <li>Node.js with npm (for npx MCP servers)</li>
            <li>Git for cloning the repository</li>
            <li>API keys for desired model providers</li>
        </ul>

        <h3>Installation Steps</h3>
        <div class="info">
            Follow these steps in order to set up the project correctly.
        </div>

        <h4>1. Clone and Setup Environment</h4>
        <div class="code-block">
# Clone the repository
git clone &lt;repository-url&gt;
cd openai_agents_test

# Create virtual environment (recommended)
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install base dependencies
pip install openai-agents python-dotenv
        </div>

        <h4>2. Install Node.js Dependencies</h4>
        <div class="code-block">
# Install npx globally (if not already installed)
npm install -g npx

# Verify npx installation
npx --version
        </div>

        <h4>3. Configure API Keys</h4>
        <div class="code-block">
# Create .env file in project root
cp .env.example .env  # If example exists
# Or create manually with your API keys

# For AWS Bedrock example
cd mcp/aws_claude_openaisdk_example
pip install -r requirements.txt

# For Brave Search example
cd mcp/brave_search_example
pip install python-dotenv agents
        </div>
    </div>

    <div class="section" id="api">
        <h2>7. API Documentation</h2>

        <h3>Agent Configuration</h3>
        <div class="code-block">
# Basic Agent Creation
agent = Agent(
    name="Agent Name",
    instructions="Detailed instructions for the agent",
    model="gpt-4o-mini"  # or external model
)

# External Model Configuration
external_client = AsyncOpenAI(
    base_url='https://api.deepinfra.com/v1/openai',
    api_key='your_api_key',
)

external_model = OpenAIChatCompletionsModel(
    model="deepseek-ai/DeepSeek-V3-0324",
    openai_client=external_client,
)

agent = Agent(
    name="Agent Name",
    instructions="Instructions",
    model=external_model
)
        </div>

        <h3>Runner Execution</h3>
        <div class="code-block">
# Synchronous Execution
result = Runner.run_sync(
    starting_agent=agent,
    input="Your prompt here"
)
output = result.final_output

# Asynchronous Execution
result = await Runner.run(
    starting_agent=agent,
    input="Your prompt here"
)
output = result.final_output
        </div>

        <h3>MCP Server Integration</h3>
        <div class="code-block">
# Filesystem MCP Server
async with MCPServerStdio(
    name="Filesystem Server",
    params={
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/directory"]
    },
) as server:
    agent = Agent(
        name="Assistant",
        instructions="Use filesystem tools to read and analyze files",
        model="gpt-4o-mini",
        mcp_servers=[server],
    )

    result = await Runner.run(starting_agent=agent, input="List the files")

# Brave Search MCP Server
async with MCPServerStdio(
    name="Brave Search Service",
    params={
        "command": "npx",
        "args": ["-y", "@modelcontextprotocol/server-brave-search"],
        "env": {"BRAVE_API_KEY": "your_api_key"}
    },
) as server:
    agent = Agent(
        name="Search Assistant",
        instructions="Use search tools to find information online",
        model="gpt-4",
        mcp_servers=[server],
    )
        </div>

        <h3>Model Provider APIs</h3>
        <table>
            <tr>
                <th>Provider</th>
                <th>Model String</th>
                <th>Base URL</th>
            </tr>
            <tr>
                <td>OpenAI</td>
                <td>gpt-4o-mini</td>
                <td>Default (OpenAI API)</td>
            </tr>
            <tr>
                <td>DeepInfra</td>
                <td>deepseek-ai/DeepSeek-V3-0324</td>
                <td>https://api.deepinfra.com/v1/openai</td>
            </tr>
            <tr>
                <td>OpenRouter</td>
                <td>google/gemini-2.5-pro-preview-03-25</td>
                <td>https://openrouter.ai/api/v1</td>
            </tr>
            <tr>
                <td>Google Direct</td>
                <td>gemini-2.5-pro-preview-03-25</td>
                <td>https://generativelanguage.googleapis.com/v1beta/openai/</td>
            </tr>
            <tr>
                <td>AWS Bedrock</td>
                <td>litellm/bedrock/anthropic.claude-sonnet-4-*</td>
                <td>Via LiteLLM</td>
            </tr>
        </table>
    </div>

    <div class="section" id="examples">
        <h2>8. Code Examples</h2>

        <h3>Basic Story Generation (openai_test.py)</h3>
        <div class="code-block">
from agents import Agent, Runner

# Define agents
outline_generator = Agent(
    name="Story Outline Generator",
    instructions="Generate a detailed outline for a story based on the provided prompt.",
    model="gpt-4o-mini"
)

story_writer = Agent(
    name="Story Writer",
    instructions="Write a compelling story based on the provided outline.",
    model="gpt-4o-mini"
)

story_editor = Agent(
    name="Story Editor",
    instructions="Edit the story for grammar, style, and coherence.",
    model="gpt-4o-mini"
)

# Execute workflow
def generate_story(prompt):
    # Step 1: Generate outline
    outline_result = Runner.run_sync(
        starting_agent=outline_generator,
        input=prompt
    )
    outline = outline_result.final_output

    # Step 2: Write story
    story_result = Runner.run_sync(
        starting_agent=story_writer,
        input=outline
    )
    story = story_result.final_output

    # Step 3: Edit story
    edited_story_result = Runner.run_sync(
        starting_agent=story_editor,
        input=story
    )
    final_story = edited_story_result.final_output

    return final_story

# Usage
story_prompt = "A story about a young girl who discovers she has the ability to time travel."
final_story = generate_story(story_prompt)
        </div>

        <h3>Multi-Model Testing with Feedback Loop (deepinfra_test1.py)</h3>
        <div class="code-block">
def generate_story_with_feedback(prompt, pass_score=8.0, max_versions=5):
    # Generate outline
    outline = run_agent(outline_generator, prompt)

    version = 1
    story = run_agent(story_writer_with_outline, outline)

    while version <= max_versions:
        # Get editor feedback
        editor_comments = run_agent(story_editor, story)
        score, comments = extract_score_and_comments(editor_comments)

        # Check if score meets threshold
        if (score is not None and score >= pass_score) or version == max_versions:
            break

        # Rewrite based on feedback
        story_with_comments = f"Rewrite based on comments:\n{comments}\n\nStory:\n{story}"
        story = run_agent(story_rewriter_with_editor_comments, story_with_comments)
        version += 1

    return story, comments, score, version

# Usage with scoring
final_story, final_comments, final_score, run_count = generate_story_with_feedback(
    "写一个中文小说，篇幅中篇，奇幻题材，主角是一只柯基",
    pass_score=9.5,
    max_versions=3
)
        </div>

        <h3>AWS Bedrock Integration</h3>
        <div class="code-block">
import asyncio
from agents import Agent, Runner
from agents.mcp import MCPServerStdio

async def run_bedrock_demo():
    # Setup MCP filesystem server
    async with MCPServerStdio(
        name="Filesystem Server, via npx",
        params={
            "command": "npx",
            "args": ["-y", "@modelcontextprotocol/server-filesystem", "./sample_files"]
        },
    ) as server:
        # Create agent with Bedrock Claude
        agent = Agent(
            name="Assistant",
            instructions="Use filesystem tools to read and analyze files.",
            model="litellm/bedrock/anthropic.claude-sonnet-4-20250514-v1:0",
            mcp_servers=[server],
        )

        # Execute queries
        result = await Runner.run(
            starting_agent=agent,
            input="Read the files and list them."
        )
        print("Response:", result.final_output)

# Run the demo
asyncio.run(run_bedrock_demo())
        </div>
    </div>

    <div class="section" id="mcp">
        <h2>9. MCP Integrations</h2>

        <h3>Available MCP Servers</h3>
        <div class="tech-grid">
            <div class="tech-card">
                <h4>Filesystem Server</h4>
                <p><strong>Package:</strong> @modelcontextprotocol/server-filesystem</p>
                <p><strong>Purpose:</strong> File operations (read, write, list directories)</p>
                <p><strong>Usage:</strong> Document analysis, file management</p>
            </div>
            <div class="tech-card">
                <h4>Brave Search Server</h4>
                <p><strong>Package:</strong> @modelcontextprotocol/server-brave-search</p>
                <p><strong>Purpose:</strong> Web search capabilities</p>
                <p><strong>Usage:</strong> Real-time information retrieval</p>
            </div>
            <div class="tech-card">
                <h4>AWS Bedrock Integration</h4>
                <p><strong>Method:</strong> LiteLLM + MCP</p>
                <p><strong>Purpose:</strong> Enterprise AI models with tool access</p>
                <p><strong>Usage:</strong> Production-grade AI applications</p>
            </div>
        </div>

        <h3>MCP Server Features</h3>
        <table>
            <tr>
                <th>Server</th>
                <th>Available Tools</th>
                <th>Key Features</th>
            </tr>
            <tr>
                <td><strong>Filesystem</strong></td>
                <td>list_directory, read_file, write_file</td>
                <td>Secure file access, directory traversal</td>
            </tr>
            <tr>
                <td><strong>Brave Search</strong></td>
                <td>brave_web_search, brave_local_search</td>
                <td>Web search, local business search, 2000 free queries/month</td>
            </tr>
            <tr>
                <td><strong>AWS Bedrock</strong></td>
                <td>All MCP tools via LiteLLM</td>
                <td>Automatic tool schema conversion, enterprise security</td>
            </tr>
        </table>

        <h3>Integration Benefits</h3>
        <ul class="feature-list">
            <li>Standardized protocol for tool integration</li>
            <li>Automatic tool discovery and registration</li>
            <li>Type-safe tool calling with validation</li>
            <li>Easy server management with context managers</li>
            <li>Robust error handling and retry mechanisms</li>
        </ul>

        <div class="info">
            <strong>MCP Advantage:</strong> The Model Context Protocol provides a standardized way to connect AI agents to external tools and services, eliminating the need for custom integrations for each tool.
        </div>
    </div>

    <div class="section" id="outputs">
        <h2>10. Test Outputs</h2>

        <h3>Output Organization</h3>
        <div class="file-tree">
TestOutput/
├── DeepInfra/                        # DeepInfra model test results
│   ├── test1_deepseek-v3-0324.txt   # Basic DeepSeek testing
│   ├── test2_deepseek-v3-0324.txt   # Advanced DeepSeek testing
│   ├── test3_deepseek-openrouter.txt # OpenRouter integration
│   ├── test4_deepseek-openrouter.txt # OpenRouter comparison
│   ├── test5_google-gemini2.5pro.txt # Google Gemini testing
│   ├── test6.html                   # HTML formatted output
│   └── test6_gemini2.5pro-deepseekv3.txt # Multi-model comparison
└── OpenAI/                          # OpenAI model test results
    ├── ### The Time Traveler's Secret.md # English story output
    └── ### 故事：小雪的时光旅行.md        # Chinese story output
        </div>

        <h3>Output Types</h3>
        <table>
            <tr>
                <th>Format</th>
                <th>Purpose</th>
                <th>Examples</th>
            </tr>
            <tr>
                <td><strong>Text Files (.txt)</strong></td>
                <td>Raw story output with metadata</td>
                <td>Model comparisons, performance testing</td>
            </tr>
            <tr>
                <td><strong>Markdown Files (.md)</strong></td>
                <td>Formatted stories with structure</td>
                <td>Final story presentations</td>
            </tr>
            <tr>
                <td><strong>HTML Files (.html)</strong></td>
                <td>Rich formatted output</td>
                <td>Web-ready story presentations</td>
            </tr>
        </table>

        <h3>Testing Scenarios</h3>
        <div class="tech-grid">
            <div class="tech-card">
                <h4>Basic Generation</h4>
                <p>Simple three-agent pipeline testing with different models</p>
                <p><strong>Files:</strong> OpenAI outputs, basic DeepInfra tests</p>
            </div>
            <div class="tech-card">
                <h4>Multi-Model Comparison</h4>
                <p>Comparative testing across different model providers</p>
                <p><strong>Files:</strong> test3-test6 series</p>
            </div>
            <div class="tech-card">
                <h4>Feedback Loop Testing</h4>
                <p>Iterative improvement with scoring and rewriting</p>
                <p><strong>Files:</strong> Advanced test outputs with scores</p>
            </div>
            <div class="tech-card">
                <h4>Multilingual Testing</h4>
                <p>Chinese and English story generation</p>
                <p><strong>Files:</strong> Both English and Chinese outputs</p>
            </div>
        </div>

        <div class="warning">
            <strong>Note:</strong> Test outputs may contain API keys or sensitive information. Review files before sharing or committing to version control.
        </div>
    </div>

    <div class="section">
        <h2>Conclusion</h2>
        <p>This project demonstrates the power and flexibility of the OpenAI Agents SDK for building sophisticated multi-agent AI systems. The combination of multiple model providers, MCP integrations, and iterative feedback loops creates a robust platform for AI-powered content generation and analysis.</p>

        <h3>Key Achievements</h3>
        <ul class="feature-list">
            <li>Successfully integrated 4+ different AI model providers</li>
            <li>Implemented robust MCP server integrations</li>
            <li>Created iterative feedback and improvement systems</li>
            <li>Demonstrated multilingual capabilities</li>
            <li>Established comprehensive testing framework</li>
        </ul>

        <h3>Future Enhancements</h3>
        <ul>
            <li>Add more MCP server integrations (database, email, etc.)</li>
            <li>Implement agent memory and context persistence</li>
            <li>Create web interface for story generation</li>
            <li>Add automated testing and CI/CD pipeline</li>
            <li>Expand to other content types beyond stories</li>
        </ul>
    </div>

</body>
</html>
