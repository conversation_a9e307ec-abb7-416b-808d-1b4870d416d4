#!/usr/bin/env python3
"""
Test script to verify that all dependencies and API keys are properly configured
before running the main Brave Search MCP example.
"""

import os
import shutil
import sys
from dotenv import load_dotenv

def check_env_file():
    """Check if .env file exists and load it"""
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("📝 Please create a .env file with your API keys.")
        print("   You can copy .env.example as a template if it exists.")
        return False

    load_dotenv()
    print("✅ .env file found and loaded")
    return True

def check_api_keys():
    """Check if required API keys are set"""
    issues = []

    openai_key = os.getenv("OPENAI_API_KEY")
    if not openai_key or openai_key == "your_openai_api_key_here":
        issues.append("OPENAI_API_KEY")

    brave_key = os.getenv("BRAVE_API_KEY")
    if not brave_key or brave_key == "your_brave_api_key_here":
        issues.append("BRAVE_API_KEY")

    agent_model = os.getenv("AGENT_MODEL")
    if not agent_model:
        issues.append("AGENT_MODEL")

    if issues:
        print(f"❌ Missing or invalid API keys: {', '.join(issues)}")
        print("📝 Please set these in your .env file:")
        for key in issues:
            print(f"   {key}=your_actual_{key.lower()}_here")
        return False

    print("✅ All required API keys are set")
    print(f"   - OpenAI Model: {agent_model}")
    print(f"   - OpenAI Key: {openai_key[:10]}..." if openai_key else "   - OpenAI Key: Not set")
    print(f"   - Brave Key: {brave_key[:10]}..." if brave_key else "   - Brave Key: Not set")
    return True

def check_npx():
    """Check if npx is available"""
    if not shutil.which("npx"):
        print("❌ npx is not installed or not in PATH")
        print("📝 Please install Node.js and npm:")
        print("   - Visit https://nodejs.org/ to download and install Node.js")
        print("   - Or use a package manager like brew, apt, or chocolatey")
        return False

    print("✅ npx is available")
    return True

def check_python_packages():
    """Check if required Python packages are installed"""
    required_packages = ['dotenv', 'agents']
    missing_packages = []

    for package in required_packages:
        try:
            if package == 'dotenv':
                import dotenv
            elif package == 'agents':
                import agents
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"❌ Missing Python packages: {', '.join(missing_packages)}")
        print("📝 Please install them with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False

    print("✅ All required Python packages are installed")
    return True

def main():
    """Run all checks"""
    print("🔍 Checking Brave Search MCP Example Configuration...")
    print("=" * 60)

    checks = [
        ("Environment file", check_env_file),
        ("Python packages", check_python_packages),
        ("NPX availability", check_npx),
        ("API keys", check_api_keys),
    ]

    all_passed = True

    for check_name, check_func in checks:
        print(f"\n📋 {check_name}:")
        if not check_func():
            all_passed = False

    print("\n" + "=" * 60)

    if all_passed:
        print("🎉 All checks passed! You can now run the main example:")
        print("   python main.py")
    else:
        print("❌ Some checks failed. Please fix the issues above before running the example.")
        sys.exit(1)

if __name__ == "__main__":
    main()